import pyrebase
from app.core.config import settings

# Firebase Configuration
firebase_config = {
    "apiKey": settings.FIREBASE_API_KEY,
    "authDomain": settings.FIREBASE_AUTH_DOMAIN,
    "projectId": settings.FIREBASE_PROJECT_ID,
    "storageBucket": settings.FIREBASE_STORAGE_BUCKET,
    "messagingSenderId": settings.FIREBASE_MESSAGING_SENDER_ID,
    "appId": settings.FIREBASE_APP_ID,
    "measurementId": settings.FIREBASE_MEASUREMENT_ID,
    "databaseURL": settings.FIREBASE_DATABASE_URL
}

# Initialize Pyrebase
firebase = pyrebase.initialize_app(firebase_config)
auth = firebase.auth()

class PyrebaseClient:
    @staticmethod
    async def sign_in_with_email_password(email: str, password: str):
        try:
            return auth.sign_in_with_email_and_password(email, password)
        except Exception as e:
            raise e

    @staticmethod
    async def sign_up_with_email_password(email: str, password: str):
        try:
            return auth.create_user_with_email_and_password(email, password)
        except Exception as e:
            raise e

    @staticmethod
    async def send_password_reset_email(email: str):
        try:
            return auth.send_password_reset_email(email)
        except Exception as e:
            raise e

pyrebase_client = PyrebaseClient()


