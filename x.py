import psycopg2
from psycopg2 import OperationalError

def create_connection():
    try:
        connection = psycopg2.connect(
            host="revupai-poc-rds.cv8gyg0q6bvp.us-east-1.rds.amazonaws.com",
            port="5444",
            database="postgres",
            user="postgres",
            password="jjUdhweoiasdbGdIUUd"
        )
        print("✅ PostgreSQL database connected successfully")
        return connection
    except OperationalError as e:
        print("❌ Connection failed")
        print(e)
        return None

def show_tables(conn):
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_schema, table_name 
            FROM information_schema.tables 
            WHERE table_type='BASE TABLE' AND table_schema NOT IN ('pg_catalog', 'information_schema');
        """)
        tables = cursor.fetchall()
        print("📋 Available Tables:")
        for schema, table in tables:
            print(f"- {schema}.{table}")
        cursor.close()
    except Exception as e:
        print("⚠️ Error fetching tables:", e)

def show_users_table(conn):
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM public.users;")
        rows = cursor.fetchall()
        colnames = [desc[0] for desc in cursor.description]
        print("👥 Users Table Data:")
        print(" | ".join(colnames))  # Print column headers
        for row in rows:
            print(" | ".join(str(item) for item in row))
        cursor.close()
    except Exception as e:
        print("⚠️ Error fetching users table data:", e)

# Main execution
conn = create_connection()

if conn:
    show_tables(conn)
    print("\nFetching data from 'users' table...\n")
    show_users_table(conn)
    conn.close()
    print("🔌 Connection closed")

