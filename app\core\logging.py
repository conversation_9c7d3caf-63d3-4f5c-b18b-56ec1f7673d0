import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Union
import secrets

from loguru import logger

from app.core.config import settings, EnvironmentType

class InterceptHandler(logging.Handler):
    """
    Intercept standard logging messages toward Loguru
    """
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )

class CustomJSONFormatter:
    """
    Custom JSON formatter for structured logging
    """
    def __call__(self, record):
        # Create log data with safe access
        log_data = {
            "timestamp": record["time"].isoformat(),
            "level": record["level"].name,
            "message": record["message"],
            "module": record["name"],
            "function": record["function"],
            "line": record["line"],
            "process_id": record["process"].id,
        }

        # Safely access extra fields
        extra = record.get("extra", {})
        if "request_id" in extra:
            log_data["request_id"] = extra["request_id"]
        
        if "duration" in extra:
            log_data["duration_ms"] = extra["duration"]
        
        if "exception" in record:
            log_data["exception"] = record["exception"]

        return json.dumps(log_data)

def setup_logging():
    """Configure logging with loguru"""
    # Remove default logger
    logger.remove()
    
    # Create logs directory
    logs_path = Path("logs")
    logs_path.mkdir(exist_ok=True)

    # Simple format that doesn't rely on request_id
    simple_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<yellow>{function}</yellow>:<white>{line}</white> | "
        "<level>{message}</level>"
    )

    # Configure handlers based on environment
    logger.add(
        sys.stderr,
        level=settings.LOG_LEVEL,
        format=simple_format,
        colorize=True
    )

    # Add file logging in production
    if settings.ENVIRONMENT == EnvironmentType.PRODUCTION:
        logger.add(
            logs_path / "app.log",
            level=settings.LOG_LEVEL,
            format=CustomJSONFormatter(),
            rotation=settings.LOG_ROTATION,
            retention=settings.LOG_RETENTION,
            compression="zip"
        )

    # Intercept standard logging
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # Update logging levels for some third-party packages
    for _log in ["uvicorn", "uvicorn.error", "fastapi"]:
        _logger = logging.getLogger(_log)
        _logger.handlers = [InterceptHandler()]
        _logger.propagate = False

    return logger

def get_request_id() -> str:
    """Generate a unique request ID"""
    return f"{datetime.now().strftime('%Y%m%d%H%M%S')}-{secrets.token_hex(6)}"
