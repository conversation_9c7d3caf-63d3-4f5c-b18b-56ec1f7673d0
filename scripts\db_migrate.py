#!/usr/bin/env python
"""
Database Migration Helper Script

This script simplifies the process of creating and applying database migrations.
It provides commands to:
1. Generate new migrations (with auto-detection of changes)
2. Apply migrations to the database
3. Downgrade migrations
4. Show migration history
5. Verify migration status

Usage:
    python scripts/db_migrate.py generate "Migration message"
    python scripts/db_migrate.py upgrade [revision]
    python scripts/db_migrate.py downgrade [revision]
    python scripts/db_migrate.py history
    python scripts/db_migrate.py current
    python scripts/db_migrate.py verify
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

# Import Alembic components
from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory
from alembic.runtime.migration import MigrationContext

# Import project settings
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger('db_migrate')

def get_alembic_config():
    """Get the Alembic configuration."""
    # Create Alembic config
    config_path = os.path.join(ROOT_DIR, "alembic.ini")
    alembic_cfg = Config(config_path)

    # Override the sqlalchemy.url with the one from settings
    database_url = str(settings.DATABASE_URL)
    alembic_cfg.set_main_option("sqlalchemy.url", database_url)

    return alembic_cfg

def setup_alembic_config():
    """Set up the Alembic configuration with the correct database URL."""
    # Ensure the alembic directory exists
    alembic_dir = ROOT_DIR / "alembic"
    if not alembic_dir.exists():
        logger.error("Alembic directory not found. Make sure you're running this script from the project root.")
        sys.exit(1)

    # Ensure the versions directory exists
    versions_dir = alembic_dir / "versions"
    if not versions_dir.exists():
        os.makedirs(versions_dir)
        logger.info(f"Created versions directory: {versions_dir}")

    return get_alembic_config()

def run_alembic_command(cmd_func, *args, **kwargs):
    """Run an Alembic command using the Python API."""
    config = get_alembic_config()

    try:
        # Redirect stdout to capture output
        from io import StringIO
        import contextlib

        output = StringIO()
        with contextlib.redirect_stdout(output):
            cmd_func(config, *args, **kwargs)

        print(output.getvalue())
        return True
    except Exception as e:
        logger.error(f"Error executing Alembic command: {e}")
        return False

def generate_migration(message):
    """Generate a new migration with auto-detection of changes."""
    print(f"Generating new migration: {message}")
    return run_alembic_command(command.revision, autogenerate=True, message=message)

def upgrade_database(revision="head"):
    """Upgrade the database to the specified revision (default: head)."""
    print(f"Upgrading database to revision: {revision}")
    return run_alembic_command(command.upgrade, revision)

def downgrade_database(revision="-1"):
    """Downgrade the database by the specified number of revisions (default: -1)."""
    print(f"Downgrading database by {revision} revision(s)")
    return run_alembic_command(command.downgrade, revision)

def show_history():
    """Show the migration history."""
    print("Migration history:")
    return run_alembic_command(command.history)

def show_current():
    """Show the current migration revision."""
    print("Current migration revision:")
    return run_alembic_command(command.current)

def verify_migrations():
    """Verify that the database matches the migrations."""
    print("Verifying migrations...")

    try:
        # Get the Alembic config
        config = get_alembic_config()

        # Get the script directory
        script = ScriptDirectory.from_config(config)

        # Get the current head revision
        head_revision = script.get_current_head()

        # Get the current database revision
        from sqlalchemy import create_engine
        engine = create_engine(str(settings.DATABASE_URL))
        with engine.connect() as connection:
            context = MigrationContext.configure(connection)
            current_revision = context.get_current_revision()

        if current_revision == head_revision:
            print("✅ Database is up to date with migrations")
            print(f"Current revision: {current_revision}")
            return True
        else:
            print("❌ Database is not up to date with migrations")
            print(f"Current database revision: {current_revision}")
            print(f"Latest migration revision: {head_revision}")
            print("Run 'python scripts/db_migrate.py upgrade' to apply pending migrations")
            return False
    except Exception as e:
        print(f"Error verifying migrations: {e}")
        return False

def main():
    """Main function to parse arguments and execute commands."""
    parser = argparse.ArgumentParser(description="Database migration helper")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Generate migration command
    generate_parser = subparsers.add_parser("generate", help="Generate a new migration")
    generate_parser.add_argument("message", help="Migration message")

    # Upgrade command
    upgrade_parser = subparsers.add_parser("upgrade", help="Upgrade the database")
    upgrade_parser.add_argument("revision", nargs="?", default="head", help="Revision to upgrade to (default: head)")

    # Downgrade command
    downgrade_parser = subparsers.add_parser("downgrade", help="Downgrade the database")
    downgrade_parser.add_argument("revision", nargs="?", default="-1", help="Number of revisions to downgrade (default: -1)")

    # History command
    subparsers.add_parser("history", help="Show migration history")

    # Current command
    subparsers.add_parser("current", help="Show current migration revision")

    # Verify command
    subparsers.add_parser("verify", help="Verify migrations")

    args = parser.parse_args()

    # Set up Alembic configuration
    setup_alembic_config()

    # Execute the requested command
    if args.command == "generate":
        generate_migration(args.message)
    elif args.command == "upgrade":
        upgrade_database(args.revision)
    elif args.command == "downgrade":
        downgrade_database(args.revision)
    elif args.command == "history":
        show_history()
    elif args.command == "current":
        show_current()
    elif args.command == "verify":
        verify_migrations()
    else:
        parser.print_help()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nError: {e}")
        print("\nFor help, run: python scripts/db_migrate.py --help")
        sys.exit(1)
