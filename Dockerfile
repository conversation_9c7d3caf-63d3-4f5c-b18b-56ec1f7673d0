FROM python:3.9-slim

WORKDIR /app/

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . /app/

# Set PYTHONPATH
ENV PYTHONPATH=/app

# Create .env from .env.example if .env doesn't exist
RUN if [ ! -f .env ]; then cp .env.example .env; fi

# Create a non-root user and switch to it
RUN adduser --disabled-password --gecos "" appuser && \
    chown -R appuser:appuser /app
USER appuser

# Performance tuning
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONOPTIMIZE=2
ENV PYTHONHASHSEED=random

# Configure uvicorn workers
ENV WEB_CONCURRENCY=4
ENV WORKERS_PER_CORE=1
ENV MAX_WORKERS=4

# Configure connection pools
ENV POSTGRES_POOL_SIZE=5
ENV POSTGRES_MAX_OVERFLOW=10
ENV REDIS_POOL_SIZE=10

# Configure timeouts
ENV WORKER_TIMEOUT=60
ENV GRACEFUL_TIMEOUT=30
ENV KEEP_ALIVE=5

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run the watcher instead of directly running uvicorn
CMD ["python", "watch.py"]





