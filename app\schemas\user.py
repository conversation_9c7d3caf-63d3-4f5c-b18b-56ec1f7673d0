# app/schemas/user.py
from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    email: Optional[EmailStr] = Field(None, description="User's email address")
    full_name: Optional[str] = Field(None, description="User's full name")
    phone_number: Optional[str] = Field(None, description="User's phone number")
    is_active: Optional[bool] = Field(True, description="Whether the user account is active")
    is_verified: Optional[bool] = Field(False, description="Whether the user's email is verified")

class UserCreate(UserBase):
    firebase_uid: str = Field(..., description="User's Firebase UID")

class UserUpdate(BaseModel):
    full_name: Optional[str] = Field(None, description="User's full name")
    phone_number: Optional[str] = Field(None, description="User's phone number")

class UserInDBBase(UserBase):
    id: str = Field(..., description="Unique identifier for the user")
    created_at: datetime = Field(..., description="When the user was created")
    updated_at: Optional[datetime] = Field(None, description="When the user was last updated")

    class Config:
        orm_mode = True

class User(UserInDBBase):
    pass

class UserInDB(UserInDBBase):
    firebase_uid: str = Field(..., description="User's Firebase UID")
