[tool.poetry]
name = "synapseai"
version = "0.1.0"
description = "A professional FastAPI application with a clean architecture"
authors = ["Your Name <<EMAIL>>"]
packages = [
    { include = "app" }
]

[tool.poetry.dependencies]
python = "^3.9"
firebase-admin = "^6.3.0"
fastapi = "^0.103.1"
uvicorn = "^0.23.2"
pydantic = "^2.3.0"
pydantic-settings = "^2.0.3"
sqlalchemy = "^2.0.20"
alembic = "^1.12.0"
psycopg2-binary = "^2.9.7"
python-jose = "^3.3.0"
passlib = "^1.7.4"
bcrypt = "^4.0.1"
python-multipart = "^0.0.6"
email-validator = "^2.0.0"
httpx = "^0.24.1"
python-dotenv = "^1.0.0"
loguru = "^0.7.0"
tenacity = "^8.2.3"
asyncpg = "^0.28.0"
watchfiles = "^0.21.0"
langchain = "0.1.7"
langchain-community = "0.0.20"  # Updated to match langchain's requirements
langchain-core = "0.1.22"
langchain-google-genai = "0.0.5"
langgraph = "0.0.20"
google-generativeai = "0.3.2"
pyrebase4= "4.7.1"
sib-api-v3-sdk = "^7.6.0"

[tool.poetry.dev-dependencies]
pytest = "^7.4.0"
pytest-cov = "^4.1.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.5.1"
pre-commit = "^3.3.3"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3










