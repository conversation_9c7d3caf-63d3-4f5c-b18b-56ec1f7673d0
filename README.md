<div align="center">
  <h1>🧠 SynapseAI</h1>
  <p><i>A professional FastAPI application revolutionizing AI-powered education</i></p>
  
  [![FastAPI](https://img.shields.io/badge/FastAPI-005571?style=for-the-badge&logo=fastapi)](https://fastapi.tiangolo.com/)
  [![Python](https://img.shields.io/badge/Python-3.9+-blue?style=for-the-badge&logo=python&logoColor=white)](https://www.python.org)
  [![PostgreSQL](https://img.shields.io/badge/PostgreSQL-336791?style=for-the-badge&logo=postgresql&logoColor=white)](https://www.postgresql.org/)
  [![Docker](https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white)](https://www.docker.com/)
</div>

## 🌟 Features

- 🚀 **FastAPI Framework**: High-performance async API with automatic OpenAPI docs
- 🔐 **Advanced Auth**: JWT authentication + Firebase integration
- 🤖 **AI Integration**: Powered by Google's Gemini AI model
- 📊 **Smart Database**: Async SQLAlchemy with PostgreSQL
- 🔄 **Auto Migrations**: Alembic for seamless database updates
- 🎯 **Clean Architecture**: Modular and maintainable codebase
- 🔍 **Comprehensive Testing**: Pytest with async support
- 🐋 **Containerization**: Docker & Docker Compose ready
- 📝 **Auto Documentation**: Swagger UI & ReDoc
- 🚦 **Rate Limiting**: Built-in request throttling
- 📧 **Email Integration**: Automated notifications via Brevo
- 🔄 **Redis Caching**: High-performance data caching

## 🚀 Quick Start

### 1️⃣ Prerequisites

```bash
# Required
Python 3.9+
PostgreSQL
Redis

# Optional but recommended
Poetry
Docker
```

### 2️⃣ Local Setup

```bash
# Clone repository
git clone https://github.com/yourusername/synapseai.git
cd synapseai

# Set up environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies (choose one)
pip install -r requirements.txt
# OR
poetry install

# Configure environment
cp .env.example .env
# Edit .env with your settings

# Run migrations
alembic upgrade head

# Start server
python watch.py  # Includes hot reload
```

### 3️⃣ Docker Setup

```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 📚 API Documentation

Once running, access:
- 🔷 Swagger UI: `http://localhost:8001/docs`
- 📘 ReDoc: `http://localhost:8001/redoc`

## 🏗️ Project Structure

```
SynapseAI/
├── 📁 app/
│   ├── api/          # API endpoints
│   ├── core/         # Core configurations
│   ├── db/           # Database models & sessions
│   ├── schemas/      # Pydantic models
│   └── utils/        # Helper utilities
├── 📁 alembic/       # Database migrations
├── 📁 tests/         # Test suite
└── 📁 docker/        # Docker configurations
```

## 🛠️ Development Commands

```bash
# Testing
pytest                                    # Run tests
pytest --cov=app tests/                  # Test coverage

# Database
alembic revision --autogenerate -m "msg" # Create migration
alembic upgrade head                     # Apply migrations

# Code Quality
black .                                  # Format code
isort .                                  # Sort imports
flake8                                   # Lint code
```

## 🚀 Deployment

### Render.com Deployment

1. **Database Setup**:
   - Create new PostgreSQL instance
   - Save connection details

2. **Environment Setup**:
   - Configure `.env.prod`
   - Set up environment variables

3. **Deploy**:
   - Connect GitHub repository
   - Configure build settings
   - Deploy web service

Detailed deployment guide in [DEPLOYMENT.md](DEPLOYMENT.md)

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- FastAPI framework
- SQLAlchemy ORM
- Google Gemini AI
- Firebase Auth
- Brevo Email Service

---
<div align="center">
  Made with ❤️ by Harsh Kumar
</div>


