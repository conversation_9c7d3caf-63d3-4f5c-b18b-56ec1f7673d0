from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Any, Dict, List
from sqlalchemy import select

from app.core.config import settings
from app.db.session import get_db
from app.db.models.user import User
from app.db.models.user_profile import UserProfile, UserType, StudyGoal
from app.schemas.user_profile import (
    UserProfileCreate, 
    UserProfileUpdate, 
    UserProfile as UserProfileSchema
)
from app.api.v1.deps import get_current_user

router = APIRouter()

def calculate_profile_completion(profile: UserProfile) -> int:
    """Calculate profile completion percentage"""
    fields = ["name", "education_level", "user_type", "subjects", "study_goals"]
    filled_fields = sum(1 for field in fields if getattr(profile, field) is not None and getattr(profile, field) != [])
    return int((filled_fields / len(fields)) * 100)

@router.get("/me", response_model=UserProfileSchema)
async def get_user_profile(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Get current user's profile"""
    query = select(UserProfile).where(UserProfile.user_id == current_user.id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Profile not found"
        )
    
    return profile

@router.post("/setup", response_model=UserProfileSchema)
async def create_user_profile(
    profile_in: UserProfileCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Create user profile"""
    # Check if profile already exists
    query = select(UserProfile).where(UserProfile.user_id == current_user.id)
    result = await db.execute(query)
    existing_profile = result.scalar_one_or_none()
    
    if existing_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Profile already exists"
        )
    
    # Create new profile
    profile = UserProfile(
        user_id=current_user.id,
        **profile_in.dict(exclude_unset=True)
    )
    
    # Calculate profile completion
    profile.profile_completion = calculate_profile_completion(profile)
    
    db.add(profile)
    await db.commit()
    await db.refresh(profile)
    
    return profile

@router.put("/update", response_model=UserProfileSchema)
async def update_user_profile(
    profile_in: UserProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Update user profile"""
    # Get existing profile
    query = select(UserProfile).where(UserProfile.user_id == current_user.id)
    result = await db.execute(query)
    profile = result.scalar_one_or_none()
    
    if not profile:
        # Create profile if it doesn't exist
        profile = UserProfile(user_id=current_user.id)
        db.add(profile)
    
    # Update profile fields
    for field, value in profile_in.dict(exclude_unset=True).items():
        setattr(profile, field, value)
    
    # Recalculate profile completion
    profile.profile_completion = calculate_profile_completion(profile)
    
    await db.commit()
    await db.refresh(profile)
    
    return profile

@router.get("/types", response_model=Dict[str, List[str]])
async def get_user_types_and_goals() -> Any:
    """Get available user types and study goals"""
    return {
        "user_types": [t.value for t in UserType],
        "study_goals": [g.value for g in StudyGoal]
    }