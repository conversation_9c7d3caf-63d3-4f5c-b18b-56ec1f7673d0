"""
<PERSON><PERSON><PERSON> to test connection to the database and query Event data.
This script uses SQLAlchemy to connect to the PostgreSQL database.
"""
import os
import sys
from datetime import datetime, timedelta
import uuid
from sqlalchemy import create_engine, Column, String, DateTime, <PERSON>olean, ForeignKey, text, inspect
from sqlalchemy.orm import sessionmaker, declarative_base, relationship
from sqlalchemy.sql import func

# Define the database connection parameters
DB_HOST = "revupai-poc-rds.cv8gyg0q6bvp.us-east-1.rds.amazonaws.com"
DB_PORT = "5444"
DB_USER = "postgres"
DB_PASSWORD = "jjUdhweoiasdbGdIUUd"
DB_NAME = "postgres"  # Default database name, change if needed

# Create the database URL
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Create Base class for SQLAlchemy models
Base = declarative_base()

# Define the Event model
class Event(Base):
    __tablename__ = "events"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", name="fk_event_user"), nullable=False, index=True)
    title = Column(String, nullable=False)
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=False)
    is_all_day = Column(Boolean, default=False)
    description = Column(String, nullable=True)
    background_color = Column(String, nullable=True)
    priority = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

# Define the User model (simplified for testing)
class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, index=True)
    full_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship with Event model
    events = relationship("Event", backref="user")

def test_connection():
    """Test the database connection"""
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
        
        return engine
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        sys.exit(1)

def list_tables(engine):
    """List all tables in the database"""
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print("\n📋 Tables in the database:")
        for table in tables:
            print(f"  - {table}")
        return tables
    except Exception as e:
        print(f"❌ Failed to list tables: {str(e)}")
        return []

def check_events_table(engine):
    """Check if events table exists and show its structure"""
    try:
        inspector = inspect(engine)
        if "events" in inspector.get_table_names():
            print("\n✅ Events table exists")
            columns = inspector.get_columns("events")
            print("\n📊 Events table structure:")
            for column in columns:
                print(f"  - {column['name']}: {column['type']}")
            return True
        else:
            print("\n❌ Events table does not exist")
            return False
    except Exception as e:
        print(f"❌ Failed to check events table: {str(e)}")
        return False

def query_events(engine):
    """Query events from the database"""
    try:
        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Query all events
        events = session.query(Event).all()
        
        print(f"\n📅 Found {len(events)} events:")
        for event in events:
            print(f"\nEvent ID: {event.id}")
            print(f"Title: {event.title}")
            print(f"User ID: {event.user_id}")
            print(f"Start Time: {event.start_time}")
            print(f"End Time: {event.end_time}")
            print(f"Is All Day: {event.is_all_day}")
            print(f"Priority: {event.priority}")
            print(f"Created At: {event.created_at}")
        
        session.close()
        return events
    except Exception as e:
        print(f"❌ Failed to query events: {str(e)}")
        return []

def create_test_event(engine):
    """Create a test event in the database"""
    try:
        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # First, check if we have any users
        users = session.query(User).all()
        
        if not users:
            print("\n⚠️ No users found in the database. Creating a test user...")
            test_user = User(
                id=str(uuid.uuid4()),
                email="<EMAIL>",
                full_name="Test User"
            )
            session.add(test_user)
            session.commit()
            user_id = test_user.id
            print(f"✅ Created test user with ID: {user_id}")
        else:
            user_id = users[0].id
            print(f"\n✅ Using existing user with ID: {user_id}")
        
        # Create a test event
        now = datetime.now()
        test_event = Event(
            id=str(uuid.uuid4()),
            user_id=user_id,
            title="Test Event",
            start_time=now,
            end_time=now + timedelta(hours=1),
            is_all_day=False,
            description="This is a test event created by the test script",
            background_color="#4285F4",
            priority="medium"
        )
        
        session.add(test_event)
        session.commit()
        
        print(f"\n✅ Created test event with ID: {test_event.id}")
        session.close()
        return test_event.id
    except Exception as e:
        print(f"❌ Failed to create test event: {str(e)}")
        return None

def main():
    """Main function to run the tests"""
    print("\n🔍 Testing database connection and Event data...")
    print(f"🔗 Connecting to: {DB_HOST}:{DB_PORT} as {DB_USER}")
    
    # Test connection
    engine = test_connection()
    if not engine:
        return
    
    # List tables
    tables = list_tables(engine)
    
    # Check events table
    events_exist = check_events_table(engine)
    
    # Query events
    if events_exist:
        events = query_events(engine)
        
        # Create a test event if no events exist
        if not events:
            print("\n⚠️ No events found. Creating a test event...")
            event_id = create_test_event(engine)
            if event_id:
                print("\n🔄 Querying events again after creating test event...")
                query_events(engine)
    
    print("\n✅ Database testing completed!")

if __name__ == "__main__":
    main()
