from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import asyncio

from app.api.v1.router import api_router
from app.core.config import settings
from app.core.logging import setup_logging
from app.middleware.middleware import setup_middleware
from app.db.session import auto_migrate, init_db
from app.core.redis import init_redis_pool

# Setup logging
logger = setup_logging()


def create_application() -> FastAPI:
    """
    Create the FastAPI application with all configurations
    """
    application = FastAPI(
        title=settings.PROJECT_NAME,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        description=""" SynapseAI is a cutting-edge AI-powered educational platform designed to revolutionize the way students learn.
        
        Key Features:
        🧠 Personalized Learning: AI-driven study plans tailored to each student's needs
        📚 Smart Content Analysis: Instant summaries and key insights from study materials
        🎯 Adaptive Testing: Dynamic assessments that adjust to your knowledge level
        💡 Interactive Learning: Engage with AI tutors for real-time help and explanations
        📊 Progress Tracking: Detailed analytics and learning progress visualization
        🤝 Collaborative Features: Connect with peers and share knowledge
        
        Our platform uses advanced AI algorithms to make learning more efficient, engaging, and accessible for everyone.
        Whether you're a student, teacher, or lifelong learner, SynapseAI helps you achieve your educational goals faster.
        """,
        version="0.1.0",
        debug=settings.DEBUG
    )

    # Setup middleware
    setup_middleware(application)

    # Include API router
    application.include_router(api_router, prefix=settings.API_V1_STR)

    @application.get("/")
    async def root():
        """Root endpoint for health check"""
        return {"message": f"Welcome to {settings.PROJECT_NAME}!"}

    @application.get("/health")
    async def health():
        """Health check endpoint"""
        return {"status": "ok"}

    @application.on_event("startup")
    async def startup_event():
        """Run startup tasks"""
        try:
            # Initialize database
            await init_db()
            logger.info("Database initialization completed successfully")
            
            # Initialize Redis
            await init_redis_pool()
            logger.info("Redis initialization completed successfully")
            
            # Skip auto-migration in development
            if not settings.DEBUG:
                await auto_migrate()
                logger.info("Database migration completed successfully")
            
        except Exception as e:
            logger.error(f"Error during startup: {e}")
            raise

    logger.info(f"Application {settings.PROJECT_NAME} started")
    return application


app = create_application()







