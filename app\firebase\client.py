# app/firebase/client.py
import json
import firebase_admin
from firebase_admin import auth, credentials
from app.core.config import settings
from typing import Optional
import os

# Initialize Firebase
if settings.FIREBASE_CREDENTIALS:
    try:
        cred_dict = json.loads(settings.FIREBASE_CREDENTIALS)
        cred = credentials.Certificate(cred_dict)
        firebase_admin.initialize_app(cred)
    except (ValueError, json.JSONDecodeError):
        # If FIREBASE_CREDENTIALS is a path to a file
        if os.path.exists(settings.FIREBASE_CREDENTIALS):
            cred = credentials.Certificate(settings.FIREBASE_CREDENTIALS)
            firebase_admin.initialize_app(cred)
        else:
            raise ValueError("Invalid Firebase credentials")
else:
    # Use default credentials (for testing or when deployed to Firebase services)
    try:
        firebase_admin.initialize_app()
    except ValueError:
        # App already initialized
        pass

class FirebaseClient:
    @staticmethod
    async def create_user(email, password, display_name=None, phone_number=None):
        """Create a new Firebase user"""
        user_kwargs = {
            "email": email,
            "password": password,
            "email_verified": False,
        }
        
        if display_name:
            user_kwargs["display_name"] = display_name
            
        if phone_number:
            user_kwargs["phone_number"] = phone_number
            
        try:
            user = auth.create_user(**user_kwargs)
            return user
        except Exception as e:
            raise e
            
    @staticmethod
    async def verify_id_token(id_token):
        """Verify the Firebase ID token"""
        try:
            decoded_token = auth.verify_id_token(id_token)
            return decoded_token
        except Exception as e:
            raise e
            
    @staticmethod
    async def generate_email_verification_link(email):
        """Generate email verification link"""
        try:
            link = auth.generate_email_verification_link(email)
            return link
        except Exception as e:
            raise e
            
    @staticmethod
    async def generate_password_reset_link(email: str, action_code_settings: Optional[auth.ActionCodeSettings] = None):
        """Generate and send password reset link"""
        try:
            if action_code_settings:
                link = auth.generate_password_reset_link(
                    email,
                    action_code_settings
                )
            else:
                link = auth.generate_password_reset_link(email)
            return link
        except Exception as e:
            print(f"Firebase password reset error: {str(e)}")
            raise e
            
    @staticmethod
    async def update_user(uid, **kwargs):
        """Update user attributes"""
        try:
            user = auth.update_user(uid, **kwargs)
            return user
        except Exception as e:
            raise e
            
    @staticmethod
    async def delete_user(uid):
        """Delete a Firebase user"""
        try:
            auth.delete_user(uid)
            return True
        except Exception as e:
            raise e
            
    @staticmethod
    async def get_user_by_email(email):
        """Get user by email"""
        try:
            user = auth.get_user_by_email(email)
            return user
        except Exception as e:
            raise e

    @staticmethod
    async def confirm_password_reset(oob_code: str, new_password: str):
        """Confirm password reset using oobCode"""
        try:
            # Verify the password reset code
            email = auth.verify_password_reset_code(oob_code)
            
            # Confirm password reset
            auth.confirm_password_reset(oob_code, new_password)
            
            return email
        except Exception as e:
            print(f"Firebase password reset confirmation error: {str(e)}")
            raise e

firebase_client = FirebaseClient()
