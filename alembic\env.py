import os
import sys
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# Add the project root directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
fileConfig(config.config_file_name)

# Import all models to ensure they're registered with SQLAlchemy
from app.db.base import Base
from app.core.config import settings

# Import all models explicitly to ensure they're registered with SQLAlchemy
# This ensures all models are available for migrations
from app.db.models.user import User
from app.db.models.user_preference import UserPreference
from app.db.models.user_profile import UserProfile
from app.db.models.event import Event

target_metadata = Base.metadata

# Override sqlalchemy.url with value from settings
# Use regular postgresql:// URL for migrations
database_url = str(settings.DATABASE_URL)
config.set_main_option("sqlalchemy.url", database_url)


def run_migrations_offline():
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,  # Compare column types during migrations
        compare_server_default=True,  # Compare server defaults
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode."""
    # Handle configuration for the connection
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,  # Compare column types during migrations
            compare_server_default=True,  # Compare server defaults
            include_schemas=True,  # Include schemas in migrations
            include_object=lambda obj, name, type_, reflected, compare_to: True,  # Include all objects
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()

