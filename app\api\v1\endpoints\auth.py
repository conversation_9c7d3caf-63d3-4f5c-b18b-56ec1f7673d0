from app.api.v1.deps import get_current_user, oauth2_scheme

from fastapi import APIRouter, HTTPException, Depends, status, Body
from fastapi.security import O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import Any, Dict
from sqlalchemy import select
import random
import string
from datetime import datetime, timedelta

from app.core.config import settings
from app.core.security import create_token, verify_token
from app.db.session import get_db
from app.db.models.user import User
from app.firebase.client import firebase_client
from app.firebase.pyrebase_client import pyrebase_client 
from app.utils.helpers import send_new_account_email, send_password_reset_email
from app.schemas.auth import (
    UserSignUp, 
    UserSignIn, 
    GoogleSignIn, 
    VerifyEmail, 
    ForgotPassword,
    ResetPassword,
    Token,
    PhoneSignIn
)
from app.utils.validators import validate_signup_data, validate_password_strength

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

# Temporary storage for verification codes
verification_codes = {}

@router.post("/signup", response_model=Dict[str, Any])
async def signup(
    user_in: UserSignUp,
    db: Session = Depends(get_db)
) -> Any:
    """
    Create new user account with email and password
    """
    try:
        # Direct validation
        validation_errors = validate_signup_data(user_in.dict())
        if validation_errors:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="\n".join(validation_errors)
            )

        # Check if user exists
        query = select(User).where(User.email == user_in.email)
        result = await db.execute(query)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="An account with this email already exists"
            )

        # Generate verification code
        verification_code = ''.join(random.choices(string.digits, k=6))
        
        # Store verification code
        verification_codes[user_in.email] = {
            'code': verification_code,
            'expires_at': datetime.utcnow() + timedelta(minutes=15),
            'user_data': user_in.dict()
        }
        
        # Send verification email
        email_sent = send_new_account_email(
            email_to=user_in.email,
            username=user_in.first_name or "there",
            verification_code=verification_code
        )
        
        if not email_sent:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send verification email"
            )
        
        return {
            "message": "Please check your email for verification code",
            "email": user_in.email
        }
        
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/verify-signup")
async def verify_signup(
    verification_data: VerifyEmail,
    db: Session = Depends(get_db)
) -> Any:
    """
    Verify email and complete user registration
    """
    try:
        email = verification_data.email
        code = verification_data.verification_code
        
        # Check if verification code exists and is valid
        if email not in verification_codes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification code"
            )
            
        stored_data = verification_codes[email]
        
        # Check if code has expired
        if datetime.utcnow() > stored_data['expires_at']:
            del verification_codes[email]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification code has expired"
            )
            
        # Direct code verification without LLM
        if code != stored_data['code']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification code"
            )
            
        # Get stored user data
        user_data = stored_data['user_data']
        
        # Create user in Firebase
        full_name = f"{user_data['first_name'] or ''} {user_data['last_name'] or ''}".strip()
        firebase_user = await firebase_client.create_user(
            email=user_data['email'],
            password=user_data['password'],
            display_name=full_name if full_name else None,
            phone_number=user_data['phone_number']
        )
        
        # Create user in database
        db_user = User(
            firebase_uid=firebase_user.uid,
            email=user_data['email'],
            full_name=full_name if full_name else None,
            phone_number=user_data['phone_number'],
            is_verified=True  
        )
        
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)
        
        # Clean up verification code
        del verification_codes[email]
        
        return {
            "message": "Email verified and account created successfully. You can now login.",
            "email": email
        }
        
    except Exception as e:
        await db.rollback()
        # Remove LLM error analysis
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
) -> Any:
    """OAuth2 compatible token login"""
    try:
        # Authenticate with Firebase
        auth_response = await pyrebase_client.sign_in_with_email_password(
            email=form_data.username,
            password=form_data.password
        )
        
        user = await firebase_client.get_user_by_email(form_data.username)
        
        # Get user from database
        query = select(User).where(User.firebase_uid == user.uid)
        result = await db.execute(query)
        db_user = result.scalar_one_or_none()
        
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User not found in database"
            )
            
        # Create access and refresh tokens
        access_token_data = create_token(subject=user.uid, token_type="access")
        refresh_token_data = create_token(subject=user.uid, token_type="refresh")
        
        return {
            "access_token": access_token_data["token"],
            "refresh_token": refresh_token_data["token"],
            "token_type": "bearer",
            "expires_in": access_token_data["expires_in"]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Login failed: {str(e)}"
        )

@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_token: str = Body(..., embed=True),
    db: Session = Depends(get_db)
) -> Any:
    """Get new access token using refresh token"""
    try:
        # Verify refresh token
        payload = verify_token(refresh_token)
        
        # Check if it's actually a refresh token
        if payload.get("type") != "refresh":
            raise ValueError("Invalid token type")
        
        firebase_uid = payload.get("sub")
        if not firebase_uid:
            raise ValueError("Invalid token payload")
            
        # Verify user exists
        query = select(User).where(User.firebase_uid == firebase_uid)
        result = await db.execute(query)
        db_user = result.scalar_one_or_none()
        
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
            
        # Create new access token
        access_token_data = create_token(subject=firebase_uid, token_type="access")
        refresh_token_data = create_token(subject=firebase_uid, token_type="refresh")
        
        return {
            "access_token": access_token_data["token"],
            "refresh_token": refresh_token_data["token"],
            "token_type": "bearer",
            "expires_in": access_token_data["expires_in"]
        }
        
    except ValueError as ve:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(ve)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """Logout current user"""
    try:
        return {
            "message": "Successfully logged out",
            "email": current_user.email
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Logout failed: {str(e)}"
        )

@router.post("/google-login", response_model=Token)
async def google_login(
    google_data: GoogleSignIn,
    db: Session = Depends(get_db)
) -> Any:
    """
    Login or register with Google
    """
    try:
        # Verify the Google ID token
        decoded_token = await firebase_client.verify_id_token(google_data.id_token)
        firebase_uid = decoded_token["uid"]
        
        # Check if user exists in our database
        user = db.query(User).filter(User.firebase_uid == firebase_uid).first()
        
        if not user:
            # Get user info from Firebase
            firebase_user = await firebase_client.get_user(firebase_uid)
            
            # Create user in our database
            db_user = User(
                firebase_uid=firebase_uid,
                email=firebase_user.email,
                full_name=firebase_user.display_name,
                is_verified=firebase_user.email_verified
            )
            
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
        
        # Create access token
        access_token_data = create_token(subject=firebase_uid, token_type="access")
        access_token = access_token_data["token"]
        
        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Google authentication failed: {str(e)}"
        )

@router.post("/forgot-password")
async def forgot_password(
    forgot_pwd: ForgotPassword,
    db: Session = Depends(get_db)
) -> Any:
    try:
        # Check if user exists
        query = select(User).where(User.email == forgot_pwd.email)
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User with this email not found"
            )
        
        # Generate reset code
        reset_code = ''.join(random.choices(string.digits + string.ascii_uppercase, k=8))
        
        # Store reset code with expiration
        verification_codes[forgot_pwd.email] = {
            'code': reset_code,
            'expires_at': datetime.utcnow() + timedelta(minutes=15),
            'type': 'password_reset'
        }
        
        # Send password reset email using Brevo
        email_sent = send_password_reset_email(
            email_to=forgot_pwd.email,
            username=user.full_name or "there",
            reset_code=reset_code
        )
        
        if not email_sent:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send password reset email"
            )
        
        return {
            "message": "Password reset instructions sent to your email",
            "email": forgot_pwd.email
        }
        
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/reset-password")
async def reset_password(
    reset_data: ResetPassword,
    db: Session = Depends(get_db)
) -> Any:
    try:
        # Direct password validation
        if reset_data.password != reset_data.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Passwords do not match"
            )
        
        validation_errors = validate_password_strength(reset_data.password)
        if validation_errors:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="\n".join(validation_errors)
            )

        # Find the email associated with this verification code
        email = None
        for email_key, data in verification_codes.items():
            if data.get('code') == reset_data.verification_code and data.get('type') == 'password_reset':
                email = email_key
                stored_data = data
                break
                
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset code"
            )
            
        # Check if code has expired
        if datetime.utcnow() > stored_data['expires_at']:
            del verification_codes[email]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Reset code has expired"
            )
        
        # Get user from Firebase
        firebase_user = await firebase_client.get_user_by_email(email)
        
        # Update password in Firebase
        await firebase_client.update_user(
            uid=firebase_user.uid,
            password=reset_data.password
        )
        
        # Clean up verification code
        del verification_codes[email]
        
        return {"message": "Password reset successfully"}
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password reset failed: " + str(e)
        )
