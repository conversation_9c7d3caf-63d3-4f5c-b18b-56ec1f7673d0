# Database Migrations Guide

This guide explains how to work with database migrations in the SynapseAI project.

## Overview

SynapseAI uses Alembic for database migrations, which allows you to:
- Track changes to your database schema
- Apply changes to your database in a controlled manner
- Roll back changes if needed
- Keep your database schema in sync with your application models

## Migration Helper Script

We've created a helper script to simplify working with migrations. The script is located at `scripts/db_migrate.py`.

### Prerequisites

Make sure you have Alembic installed:

```bash
pip install alembic
```

### Commands

#### Generate a New Migration

To create a new migration that automatically detects changes in your models:

```bash
python scripts/db_migrate.py generate "Description of your changes"
```

This will:
1. Compare your SQLAlchemy models with the current database schema
2. Generate a migration file with the necessary changes
3. Save the migration file in the `alembic/versions` directory with a timestamp and your description

#### Apply Migrations

To apply all pending migrations:

```bash
python scripts/db_migrate.py upgrade
```

To apply migrations up to a specific revision:

```bash
python scripts/db_migrate.py upgrade revision_id
```

#### Downgrade Migrations

To roll back the most recent migration:

```bash
python scripts/db_migrate.py downgrade
```

To roll back multiple migrations:

```bash
python scripts/db_migrate.py downgrade -3  # Roll back 3 migrations
```

#### View Migration History

To see the history of migrations:

```bash
python scripts/db_migrate.py history
```

#### Check Current Migration

To see the current migration version:

```bash
python scripts/db_migrate.py current
```

#### Verify Migrations

To verify that your database is in sync with your migrations:

```bash
python scripts/db_migrate.py verify
```

## Adding New Models

When you create a new model:

1. Create your model file in `app/db/models/`
2. Import your model in `app/db/base.py`
3. Generate a migration using the helper script

Example:

```python
# app/db/models/new_model.py
from sqlalchemy import Column, String, Integer
from app.db.base_class import Base

class NewModel(Base):
    __tablename__ = "new_models"
    
    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    value = Column(Integer, default=0)
```

Then add it to `app/db/base.py`:

```python
# app/db/base.py
from app.db.base_class import Base  # noqa
from app.db.models.user import User  # noqa
from app.db.models.user_preference import UserPreference  # noqa
from app.db.models.user_profile import UserProfile  # noqa
from app.db.models.new_model import NewModel  # noqa  # Add this line
```

Then generate a migration:

```bash
python scripts/db_migrate.py generate "Add new_models table"
```

## Manual Migrations

Sometimes you may need to write migrations manually. The migration files are located in `alembic/versions/`.

Each migration file has `upgrade()` and `downgrade()` functions:
- `upgrade()`: Contains the changes to apply
- `downgrade()`: Contains the changes to roll back

We've created a custom template with examples of common operations in `alembic/templates/migration_template.py.mako`.

## Best Practices

1. **Always generate migrations in development, not production**
2. **Test migrations before applying them to production**
3. **Keep migrations small and focused on specific changes**
4. **Include meaningful descriptions in your migration messages**
5. **Always provide a proper `downgrade()` function**
6. **Run migrations as part of your deployment process**
7. **Back up your database before applying migrations in production**

## Troubleshooting

### Migration Conflicts

If you get conflicts between migrations:

1. Make sure all developers are working with the latest migrations
2. Resolve conflicts by manually editing the migration files
3. Test the migrations thoroughly before applying them

### Failed Migrations

If a migration fails:

1. Check the error message for details
2. Fix the issue in your models or migration file
3. If needed, roll back the failed migration: `python scripts/db_migrate.py downgrade`
4. Apply the fixed migration: `python scripts/db_migrate.py upgrade`

### Database Out of Sync

If your database is out of sync with your migrations:

1. Check the current migration: `python scripts/db_migrate.py current`
2. Verify the state: `python scripts/db_migrate.py verify`
3. Consider creating a new migration to bring the database in sync

## Further Reading

- [Alembic Documentation](https://alembic.sqlalchemy.org/en/latest/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/en/14/)
