# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=SynapseAI
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Database Settings
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/synapseai
DATABASE_TEST_URL=postgresql://postgres:postgres@localhost:5432/synapseai_test

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:55856/"]

# Logging
LOG_LEVEL=INFO

# Firebase Settings
FIREBASE_CREDENTIALS=firebase-credentials.json

# Gemini Settings
GEMINI_API_KEY=AIzaSyBxuWJ36-QTE72hmaty-Aq2WqL-dIA3c_o
GEMINI_MODEL=gemini-2.0-flash

# Email Settings
BREVO_API_KEY=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
BREVO_SENDER_EMAIL=<EMAIL>
BREVO_SENDER_NAME=SynapseAI

# Redis Settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redispassword
REDIS_DB=0
REDIS_SSL=true

