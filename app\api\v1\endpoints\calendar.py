from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, between
from typing import Any, Dict, List
from datetime import datetime, timedelta, time

from app.core.config import settings
from app.db.session import get_db
from app.db.models.user import User
from app.db.models.event import Event
from app.schemas.event import (
    EventCreate,
    EventUpdate,
    Event as EventSchema,
    EventResponse,
    EventDayRequest,
    EventRangeRequest,
    EventDeleteRequest
)
from app.api.v1.deps import get_current_user

router = APIRouter()

@router.post("/events", response_model=EventResponse)
async def create_event(
    event_in: EventCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Create a new calendar event
    """
    try:
        # Create new event with auto-generated ID
        db_event = Event(
            user_id=current_user.id,
            title=event_in.title,
            start_time=event_in.start_time,
            end_time=event_in.end_time,
            is_all_day=event_in.is_all_day,
            description=event_in.description,
            background_color=event_in.background_color,
            priority=event_in.priority
        )

        db.add(db_event)
        await db.commit()
        await db.refresh(db_event)

        return db_event

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not create event: {str(e)}"
        )

@router.post("/events/day", response_model=List[EventResponse])
async def get_events_by_day(
    request: EventDayRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get all events for a specific day
    """
    try:
        # Set time to start of day
        start_of_day = datetime.combine(request.date.date(), time.min)
        # Set time to end of day
        end_of_day = datetime.combine(request.date.date(), time.max)

        # Query events for this day
        query = select(Event).where(
            and_(
                Event.user_id == current_user.id,
                Event.start_time >= start_of_day,
                Event.start_time <= end_of_day
            )
        )

        result = await db.execute(query)
        events = result.scalars().all()

        return events

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not retrieve events: {str(e)}"
        )

@router.post("/events/week", response_model=List[EventResponse])
async def get_events_by_week(
    request: EventRangeRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get all events for a specific week (Sunday to Saturday)
    """
    try:
        # Query events for this week
        query = select(Event).where(
            and_(
                Event.user_id == current_user.id,
                Event.start_time >= request.start_time,
                Event.start_time <= request.end_time
            )
        )

        result = await db.execute(query)
        events = result.scalars().all()

        return events

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not retrieve events: {str(e)}"
        )

@router.post("/events/month", response_model=List[EventResponse])
async def get_events_by_month(
    request: EventRangeRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get all events for a specific month
    """
    try:
        # Query events for this month
        query = select(Event).where(
            and_(
                Event.user_id == current_user.id,
                Event.start_time >= request.start_time,
                Event.start_time <= request.end_time
            )
        )

        result = await db.execute(query)
        events = result.scalars().all()

        return events

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not retrieve events: {str(e)}"
        )

@router.delete("/events/{event_id}", response_model=Dict[str, Any])
async def delete_event(
    event_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Delete an event
    """
    try:
        # Find the event
        query = select(Event).where(
            and_(
                Event.id == event_id,
                Event.user_id == current_user.id
            )
        )

        result = await db.execute(query)
        event = result.scalar_one_or_none()

        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )

        # Delete the event
        await db.delete(event)
        await db.commit()

        return {"message": "Event deleted successfully"}

    except HTTPException as he:
        raise he
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not delete event: {str(e)}"
        )

@router.put("/events/{event_id}", response_model=EventResponse)
async def update_event(
    event_id: str,
    event_in: EventUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Update an event
    """
    try:
        # Find the event
        query = select(Event).where(
            and_(
                Event.id == event_id,
                Event.user_id == current_user.id
            )
        )

        result = await db.execute(query)
        event = result.scalar_one_or_none()

        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )

        # Update event fields
        event.title = event_in.title
        event.start_time = event_in.start_time
        event.end_time = event_in.end_time
        event.is_all_day = event_in.is_all_day
        event.description = event_in.description
        event.background_color = event_in.background_color
        event.priority = event_in.priority

        await db.commit()
        await db.refresh(event)

        return event

    except HTTPException as he:
        raise he
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not update event: {str(e)}"
        )
