import secrets
import socket
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from urllib.parse import urlparse
import logging
import os 
from pydantic import AnyHttpUrl, PostgresDsn, field_validator
from pydantic_settings import BaseSettings

logger = logging.getLogger(__name__)

class EnvironmentType(str, Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

class Settings(BaseSettings):
    # Environment
    ENVIRONMENT: EnvironmentType = EnvironmentType.DEVELOPMENT
    DEBUG: bool = False

    # API Settings
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30  # 30 minutes
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7     # 7 days
    PROJECT_NAME: str
    
    # CORS Settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "https://synapseai.onrender.com",
        "http://localhost:8000",
        "http://localhost:3000"
    ]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # DNS Settings
    DNS_SERVERS: List[str] = ["*******", "*******"]  # Google DNS servers
    DNS_TIMEOUT: int = 5
    DNS_RETRIES: int = 3
    
    # Database Settings
    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    DATABASE_URL: Optional[PostgresDsn] = None

    @field_validator("DATABASE_URL", mode="before")
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme="postgresql",
            username=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_SERVER"),
            path=f"{values.get('POSTGRES_DB') or ''}",
        )

    @field_validator("DATABASE_URL")
    def validate_database_url(cls, v: Union[str, PostgresDsn]) -> Union[str, PostgresDsn]:
        if v:
            try:
                # Convert PostgresDsn to string before parsing
                url_str = str(v)
                parsed = urlparse(url_str)
                socket.gethostbyname(parsed.hostname)
            except socket.gaierror:
                logger.warning(f"DNS resolution failed for {parsed.hostname}")
            return v
        return v

    # Firebase Settings
    FIREBASE_CREDENTIALS: str = ""

    # Firebase Config
    FIREBASE_API_KEY: str
    FIREBASE_AUTH_DOMAIN: str
    FIREBASE_PROJECT_ID: str
    FIREBASE_STORAGE_BUCKET: str
    FIREBASE_MESSAGING_SENDER_ID: str
    FIREBASE_APP_ID: str
    FIREBASE_MEASUREMENT_ID: str
    FIREBASE_DATABASE_URL: str

    # Gemini Settings
    GEMINI_API_KEY: str
    GEMINI_MODEL: str = "gemini-2.0-flash"

    # Logging Settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json" # or "text"
    LOG_FILE_PATH: str = "logs/app.log"
    LOG_ROTATION: str = "10 MB"
    LOG_RETENTION: str = "1 month"
    ENABLE_ACCESS_LOG: bool = True
    ENABLE_REQUEST_ID: bool = True
    
    # Security Settings
    ALGORITHM: str = "HS256"
    RATE_LIMIT_PER_MINUTE: int = 60
    SSL_ENABLED: bool = True  # Make sure this is True for production

    # Middleware Settings
    ENABLE_LOGGING_MIDDLEWARE: bool = True
    ENABLE_CORS_MIDDLEWARE: bool = True
    ENABLE_GZIP_MIDDLEWARE: bool = True
    ENABLE_RATE_LIMIT_MIDDLEWARE: bool = True
    ENABLE_TIMING_MIDDLEWARE: bool = True
    REQUEST_TIMEOUT: int = 30
    MAX_REQUEST_SIZE: int = 10_485_760  # 10MB in bytes

    # Brevo Settings
    BREVO_API_KEY: str = "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"
    BREVO_SENDER_EMAIL: str = "<EMAIL>"
    BREVO_SENDER_NAME: str = "SynapseAI"

    # Redis Settings
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = "redispassword"
    REDIS_DB: int = 0
    REDIS_SSL: bool = False
    REDIS_TIMEOUT: int = 5
    REDIS_RETRY_ON_TIMEOUT: bool = True
    REDIS_MAX_CONNECTIONS: int = 10
    REDIS_SOCKET_KEEPALIVE: bool = True

    class Config:
        case_sensitive = True
        env_file = ".env"


        
    

settings = Settings()




