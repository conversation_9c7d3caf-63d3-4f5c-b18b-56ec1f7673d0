import json
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import HTT<PERSON>Exception
from loguru import logger
import sib_api_v3_sdk
from sib_api_v3_sdk.rest import ApiException
from app.core.config import settings

def send_new_account_email(email_to: str, username: str, verification_code: str) -> bool:
    """
    Send verification email using Brevo/Sendinblue
    """
    try:
        # Initialize Brevo API configuration
        configuration = sib_api_v3_sdk.Configuration()
        configuration.api_key['api-key'] = settings.BREVO_API_KEY
        
        # Create API instance
        api_instance = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))
        
        # Email content
        subject = "Welcome to SynapseAI - Verify Your Email"
        sender = {
            "name": settings.BREVO_SENDER_NAME,
            "email": settings.BREVO_SENDER_EMAIL
        }
        to = [{"email": email_to, "name": username}]
        
        # HTML content with better styling
        html_content = f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #2c3e50;">Welcome to SynapseAI!</h2>
                        <p>Hello {username},</p>
                        <p>Thank you for signing up. To complete your registration, please use the verification code below:</p>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
                            <h1 style="color: #007bff; letter-spacing: 5px; margin: 0;">{verification_code}</h1>
                        </div>
                        <p>This code will expire in 15 minutes.</p>
                        <p style="color: #6c757d; font-size: 0.9em;">If you didn't request this verification, please ignore this email.</p>
                    </div>
                </body>
            </html>
        """
        
        # Send email
        send_smtp_email = sib_api_v3_sdk.SendSmtpEmail(
            sender=sender,
            to=to,
            subject=subject,
            html_content=html_content
        )
        
        response = api_instance.send_transac_email(send_smtp_email)
        logger.info(f"Email sent successfully to {email_to}. MessageId: {response.message_id}")
        return True
        
    except ApiException as e:
        logger.error(f"Brevo API error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error sending email: {str(e)}")
        return False


def send_password_reset_email(email_to: str, username: str, reset_code: str) -> bool:
    """
    Send password reset email using Brevo/Sendinblue
    """
    try:
        # Initialize Brevo API configuration
        configuration = sib_api_v3_sdk.Configuration()
        configuration.api_key['api-key'] = settings.BREVO_API_KEY
        
        # Create API instance
        api_instance = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))
        
        # Email content
        subject = "SynapseAI - Reset Your Password"
        sender = {
            "name": settings.BREVO_SENDER_NAME,
            "email": settings.BREVO_SENDER_EMAIL
        }
        to = [{"email": email_to, "name": username}]
        
        # HTML content with better styling
        html_content = f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #2c3e50;">Password Reset Request</h2>
                        <p>Hello {username},</p>
                        <p>We received a request to reset your password. Please use the code below to reset your password:</p>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
                            <h1 style="color: #007bff; letter-spacing: 5px; margin: 0;">{reset_code}</h1>
                        </div>
                        <p>This code will expire in 15 minutes.</p>
                        <p style="color: #6c757d; font-size: 0.9em;">If you didn't request this password reset, please ignore this email.</p>
                    </div>
                </body>
            </html>
        """
        
        # Send email
        send_smtp_email = sib_api_v3_sdk.SendSmtpEmail(
            sender=sender,
            to=to,
            subject=subject,
            html_content=html_content
        )
        
        response = api_instance.send_transac_email(send_smtp_email)
        logger.info(f"Password reset email sent successfully to {email_to}. MessageId: {response.message_id}")
        return True
        
    except ApiException as e:
        logger.error(f"Brevo API error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error sending password reset email: {str(e)}")
        return False


def format_datetime(dt: Optional[datetime]) -> Optional[str]:
    """
    Format a datetime object to ISO format.
    """
    if dt is None:
        return None
    return dt.isoformat()


def handle_error(status_code: int, detail: str) -> None:
    """
    Raise an HTTPException with the given status code and detail.
    """
    logger.error(f"Error: {status_code} - {detail}")
    raise HTTPException(status_code=status_code, detail=detail)


def parse_json_string(json_string: str) -> Dict[str, Any]:
    """
    Parse a JSON string into a dictionary.
    """
    try:
        return json.loads(json_string)
    except json.JSONDecodeError as e:
        handle_error(400, f"Invalid JSON: {str(e)}")
        return {}
