from redis import asyncio as aioredis
from app.core.config import settings

async def init_redis_pool():
    """Initialize Redis connection pool"""
    redis = await aioredis.from_url(
        f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}",
        password=settings.REDIS_PASSWORD,
        db=settings.REDIS_DB,
        ssl=settings.REDIS_SSL,
        decode_responses=True,
        socket_timeout=settings.REDIS_TIMEOUT,
        socket_keepalive=settings.REDIS_SOCKET_KEEPALIVE,
        retry_on_timeout=settings.REDIS_RETRY_ON_TIMEOUT,
        max_connections=settings.REDIS_MAX_CONNECTIONS
    )
    return redis

# Global Redis instance
redis = None

async def get_redis():
    """Get Redis connection"""
    global redis
    if redis is None:
        redis = await init_redis_pool()
    return redis