name: synapseai
runtime: python
buildCommand: pip install -r requirements.txt
startCommand: python watch.py

# Improved instance configuration
instanceType: CPU
minInstances: 1
maxInstances: 3
scaling:
  minConcurrency: 10
  maxConcurrency: 100
  targetResponseTimeMs: 1000

envVars:
    # Environment (These are correct)
    - key: ENVIRONMENT
      value: production
    - key: DEBUG
      value: false
    
    # API Settings
    - key: API_V1_STR
      value: /api/v1
    - key: PROJECT_NAME
      value: SynapseAI
    - key: SECRET_KEY
      value: your_production_secret_key_here
    - key: ACCESS_TOKEN_EXPIRE_MINUTES
      value: 30
    - key: ALGORITHM
      value: HS256
    
    # Database Configuration
    - key: POSTGRES_SERVER
      value: dpg-d06ht0hr0fns73flhsag-a.oregon-postgres.render.com
    - key: POSTGRES_USER
      value: synapseai_user
    - key: POSTGRES_PASSWORD
      value: HdigQntS8Ls3g51upwFdnV4q9EfjMgiA
    - key: POSTGRES_DB
      value: synapseai
    - key: DATABASE_URL
      fromDatabase:
        name: synapseai
        property: connectionString

    # Redis Configuration (Add connection timeout)
    - key: REDIS_HOST
      value: redis
    - key: REDIS_PORT
      value: 6379
    - key: REDIS_PASSWORD
      value: Harsh@170
    - key: REDIS_DB
      value: 0
    - key: REDIS_SSL
      value: true
    - key: REDIS_TIMEOUT
      value: 10
    - key: REDIS_RETRY_ON_TIMEOUT
      value: true
    - key: REDIS_MAX_CONNECTIONS
      value: 20
    
    # CORS and Security
    - key: BACKEND_CORS_ORIGINS
      value: '["https://synapseai.onrender.com","http://localhost:55856/"]'
    - key: SSL_ENABLED
      value: true
    - key: RATE_LIMIT_PER_MINUTE
      value: 30
    
    # Logging Configuration
    - key: LOG_LEVEL
      value: INFO
    - key: LOG_FORMAT
      value: json
    - key: ENABLE_ACCESS_LOG
      value: true
    
    # Middleware Configuration
    - key: ENABLE_LOGGING_MIDDLEWARE
      value: true
    - key: ENABLE_CORS_MIDDLEWARE
      value: true
    - key: ENABLE_GZIP_MIDDLEWARE
      value: true
    - key: ENABLE_RATE_LIMIT_MIDDLEWARE
      value: true
    - key: ENABLE_TIMING_MIDDLEWARE
      value: true
    
    # API Keys
    - key: FIREBASE_CREDENTIALS
      value: firebase-credentials.json
    - key: GEMINI_API_KEY
      value: AIzaSyBxuWJ36-QTE72hmaty-Aq2WqL-dIA3c_o
    - key: GEMINI_MODEL
      value: gemini-2.0-flash
    - key: BREVO_API_KEY
      value: 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
    - key: BREVO_SENDER_EMAIL
      value: <EMAIL>
    - key: BREVO_SENDER_NAME
      value: SynapseAI

# Health Check Configuration
healthCheckPath: /health
healthCheckTimeout: 10
healthCheckInterval: 30

# Resource Configuration
disk:
    name: data
    mountPath: /data
    sizeGB: 2

# Build Configuration
buildFilter:
    paths:
    - app/**
    - requirements.txt
    - watch.py

