from typing import Dict, List
import re

def validate_signup_data(data: Dict) -> List[str]:
    errors = []
    
    # Email validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not data.get('email') or not re.match(email_pattern, data['email']):
        errors.append("ERROR: Invalid email format")

    # Password validation
    password = data.get('password', '')
    if len(password) < 8:
        errors.append("ERROR: Password must be at least 8 characters")
    
    # Password match validation    
    if password != data.get('confirm_password'):
        errors.append("ERROR: Passwords do not match")

    
    if not data.get('first_name'):
        errors.append("ERROR: First name is required")

    return errors

def validate_password_strength(password: str) -> List[str]:
    errors = []
    if len(password) < 8:
        errors.append("ERROR: Password must be at least 8 characters")
    if not any(c.isupper() for c in password):
        errors.append("ERROR: Password must contain at least one uppercase letter")
    if not any(c.islower() for c in password):
        errors.append("ERROR: Password must contain at least one lowercase letter")
    if not any(c.isdigit() for c in password):
        errors.append("ERROR: Password must contain at least one number")
    return errors