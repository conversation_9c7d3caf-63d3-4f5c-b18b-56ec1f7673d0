from fastapi import APIRouter

from app.api.v1.endpoints import auth, user, user_profile, calendar

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(user.router, prefix="/users", tags=["users"])
api_router.include_router(user_profile.router, prefix="/profile", tags=["profile"])
api_router.include_router(calendar.router, prefix="/calendar", tags=["calendar"])




