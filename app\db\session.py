from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text, inspect
import ssl
from app.core.config import settings
from loguru import logger
from urllib.parse import urlparse, parse_qs, urlunparse
from alembic.config import Config
from alembic import command
import sqlalchemy as sa

# Process DATABASE_URL to remove sslmode and convert to asyncpg
def prepare_database_url(url: str) -> str:
    parsed = urlparse(url)
    # Remove sslmode from query parameters
    query_params = parse_qs(parsed.query)
    query_params.pop('sslmode', None)
    # Reconstruct query string
    new_query = '&'.join(f"{k}={v[0]}" for k, v in query_params.items())
    # Reconstruct URL with postgresql+asyncpg scheme
    new_parsed = parsed._replace(
        scheme='postgresql+asyncpg',
        query=new_query
    )
    return urlunparse(new_parsed)

database_url = prepare_database_url(str(settings.DATABASE_URL))

# Configure SSL context
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

# Create engine with SSL configuration
engine = create_async_engine(
    database_url,
    pool_pre_ping=True,
    pool_size=5,
    max_overflow=10,
    pool_recycle=300,
    pool_timeout=20,
    connect_args={
        "ssl": ssl_context,
        "timeout": 60,
    },
    echo=settings.DEBUG
)

AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

async def validate_database_connection():
    """Validate database connection parameters"""
    try:
        async with engine.connect() as conn:
            await conn.execute(text("SELECT 1"))
            logger.info("Database connection test successful")
        return True
    except Exception as e:
        logger.error(f"Database validation error: {str(e)}")
        return False

async def get_db() -> AsyncSession:
    """Dependency function that yields async db sessions"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

async def init_db():
    """Initialize database"""
    try:
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
            logger.info("Database initialized")
    except Exception as e:
        logger.error(f"Database initialization error: {str(e)}")
        raise

async def auto_migrate():
    """
    Automatically run migrations if tables don't exist
    """
    try:
        # Get a connection from the engine
        async with engine.begin() as conn:
            # Get the raw connection
            raw_conn = await conn.get_raw_connection()
            # Get the underlying connection
            sync_conn = raw_conn.connection
            
            # Use the engine directly for inspection instead of the connection
            inspector = inspect(engine)
            
            # Check if tables exist
            tables = await conn.run_sync(lambda sync_conn: inspector.get_table_names())
            
            if not tables:
                logger.info("No tables found, running migrations")
                # Run migrations using alembic
                config = Config("alembic.ini")
                command.upgrade(config, "head")
                logger.info("Migrations completed")
            else:
                logger.info(f"Found existing tables: {tables}")
                
    except Exception as e:
        logger.error(f"Migration error: {str(e)}")
        # Don't raise the exception to allow the application to start










