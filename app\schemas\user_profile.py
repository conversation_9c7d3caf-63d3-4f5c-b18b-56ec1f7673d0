from pydantic import BaseModel, Field
from typing import Optional, List
from enum import Enum

class UserType(str, Enum):
    STUDENT = "student"
    TEACHER = "teacher"
    PARENT = "parent"

class StudyGoal(str, Enum):
    EXAM_PREPARATION = "exam_preparation"
    SKILL_DEVELOPMENT = "skill_development"
    PERSONAL_ENRICHMENT = "personal_enrichment"
    LANGUAGE_LEARNING = "language_learning"

class UserProfileBase(BaseModel):
    name: Optional[str] = Field(None, description="User's name")
    education_level: Optional[str] = Field(None, description="User's education level")
    user_type: Optional[UserType] = Field(None, description="Type of user (student/teacher/parent)")
    subjects: Optional[List[str]] = Field(None, description="Subjects of interest")
    study_goals: Optional[List[StudyGoal]] = Field(None, description="User's study goals")

class UserProfileCreate(UserProfileBase):
    pass

class UserProfileUpdate(UserProfileBase):
    pass

class UserProfileInDBBase(UserProfileBase):
    id: str
    user_id: str
    profile_completion: int

    class Config:
        orm_mode = True

class UserProfile(UserProfileInDBBase):
    pass