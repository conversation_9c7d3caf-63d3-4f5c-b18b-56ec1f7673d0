from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# Base class without ID for creation
class EventCreateBase(BaseModel):
    title: str = Field(..., description="Title of the event")
    start_time: datetime = Field(..., description="Start time of the event")
    end_time: datetime = Field(..., description="End time of the event")
    is_all_day: bool = Field(False, description="Whether the event is an all-day event")
    description: Optional[str] = Field(None, description="Description of the event")
    background_color: Optional[str] = Field(None, description="Background color for the event")
    priority: str = Field(..., description="Priority of the event")

# Base class with ID for responses and updates
class EventBase(EventCreateBase):
    id: str = Field(..., description="Unique identifier for the event")

class EventCreate(EventCreateBase):
    pass

class EventUpdate(EventBase):
    pass

class EventInDBBase(EventBase):
    user_id: str = Field(..., description="ID of the user who owns this event")
    created_at: datetime = Field(..., description="When the event was created")
    updated_at: Optional[datetime] = Field(None, description="When the event was last updated")

    class Config:
        orm_mode = True

class Event(EventInDBBase):
    pass

class EventResponse(EventBase):
    pass

# Request models for specific operations
class EventDayRequest(BaseModel):
    date: datetime = Field(..., description="Date to get events for")

class EventRangeRequest(BaseModel):
    start_time: datetime = Field(..., description="Start time of the range")
    end_time: datetime = Field(..., description="End time of the range")

class EventDeleteRequest(BaseModel):
    id: str = Field(..., description="ID of the event to delete")
