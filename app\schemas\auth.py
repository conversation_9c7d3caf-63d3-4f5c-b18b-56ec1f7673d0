from pydantic import BaseModel, EmailStr, Field
from typing import Optional

class UserSignUp(BaseModel):
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., min_length=8, description="User's password (minimum 8 characters)")
    confirm_password: str = Field(..., min_length=8, description="Confirmation of user's password")
    first_name: Optional[str] = Field(None, description="User's first name")
    last_name: Optional[str] = Field(None, description="User's last name")
    phone_number: Optional[str] = Field(None, description="User's phone number")

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "password123",
                "confirm_password": "password123",
                "first_name": "<PERSON>",
                "last_name": "Doe",
                "phone_number": "+1234567890"
            }
        }

class UserSignIn(BaseModel):
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., description="User's password")

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "password123"
            }
        }

class GoogleSignIn(BaseModel):
    id_token: str = Field(..., description="Google authentication ID token")

class VerifyEmail(BaseModel):
    email: EmailStr = Field(..., description="User's email address")
    verification_code: str = Field(..., description="Email verification code")
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "verification_code": "123456"
            }
        }

class ForgotPassword(BaseModel):
    email: EmailStr = Field(..., description="User's email address")

class ResetPassword(BaseModel):
    verification_code: str = Field(..., description="Verification code received via email")
    password: str = Field(..., min_length=8, description="New password (minimum 8 characters)")
    confirm_password: str = Field(..., min_length=8, description="Confirmation of new password")

class SetNewPassword(BaseModel):
    password: str = Field(..., min_length=8, description="New password (minimum 8 characters)")
    confirm_password: str = Field(..., min_length=8, description="Confirmation of new password")

class Token(BaseModel):
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(..., description="Type of token (usually 'bearer')")
    expires_in: int = Field(..., description="Token expiration time in seconds")

class TokenData(BaseModel):
    uid: Optional[str] = Field(None, description="User's unique identifier")
    email: Optional[str] = Field(None, description="User's email address")
    token_type: Optional[str] = Field(None, description="Type of token") 

class PhoneSignIn(BaseModel):
    phone_number: str = Field(..., description="User's phone number")
    password: str = Field(..., description="User's password")

    class Config:
        json_schema_extra = {
            "example": {
                "phone_number": "+************",
                "password": "password123"
            }
        }
