"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import uuid
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade():
    ${upgrades if upgrades else "pass"}
    # Uncomment and modify the examples below as needed
    
    # # Create a new table
    # op.create_table(
    #     'example_table',
    #     sa.Column('id', sa.String(), primary_key=True, default=lambda: str(uuid.uuid4())),
    #     sa.Column('name', sa.String(), nullable=False),
    #     sa.Column('description', sa.Text(), nullable=True),
    #     sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
    #     sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
    # )
    
    # # Add a column to an existing table
    # op.add_column('existing_table', sa.Column('new_column', sa.String(), nullable=True))
    
    # # Create an index
    # op.create_index('idx_example_name', 'example_table', ['name'])
    
    # # Create a foreign key
    # op.create_foreign_key(
    #     'fk_example_parent',
    #     'example_table', 'parent_table',
    #     ['parent_id'], ['id'],
    #     ondelete='CASCADE'
    # )
    
    # # Create an enum type
    # op.execute("CREATE TYPE example_status AS ENUM ('active', 'inactive', 'pending')")
    
    # # Alter a column
    # op.alter_column('example_table', 'name', 
    #                 existing_type=sa.String(),
    #                 type_=sa.String(100),
    #                 nullable=False)


def downgrade():
    ${downgrades if downgrades else "pass"}
    # Uncomment and modify the examples below as needed
    # Make sure to reverse the operations in the correct order
    
    # # Drop a foreign key
    # op.drop_constraint('fk_example_parent', 'example_table', type_='foreignkey')
    
    # # Drop an index
    # op.drop_index('idx_example_name', 'example_table')
    
    # # Drop a column
    # op.drop_column('existing_table', 'new_column')
    
    # # Drop a table
    # op.drop_table('example_table')
    
    # # Drop an enum type
    # op.execute("DROP TYPE example_status")
