import os
import signal
import subprocess
import sys
from pathlib import Path
from watchfiles import run_process, DefaultFilter, Change

def cleanup_process(process):
    """Cleanup the uvicorn process and its children"""
    try:
        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
    except (ProcessLookupError, AttributeError):
        pass

def start_uvicorn():
    """Start uvicorn server with process group management"""
    port = int(os.environ.get("PORT", 8001))  
    process = subprocess.Popen(
        [
            "uvicorn",
            "app.main:app",
            "--host",
            "0.0.0.0",
            "--port",
            str(port),
            "--reload"
        ],
        preexec_fn=os.setsid
    )
    return process

def callback(changes):
    """Handle file changes"""
    print("Changes detected:")
    for change_type, path in changes:
        print(f"{change_type}: {path}")
    return True

def main():
    """Main function to run the file watcher"""
    print("Starting file watcher...")
    print("Watching directory:", str(Path.cwd()))
    print("Press Ctrl+C to stop")

    current_process = None
    try:
        current_process = start_uvicorn()
        # Wait for the process
        current_process.wait()
    except KeyboardInterrupt:
        print("\nShutting down gracefully...")
    finally:
        if current_process:
            cleanup_process(current_process)

if __name__ == "__main__":
    main()



