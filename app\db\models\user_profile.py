from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Enum, ARRAY, Integer, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
import uuid

from app.db.base_class import Base

class UserType(str, enum.Enum):
    STUDENT = "student"
    TEACHER = "teacher"
    PARENT = "parent"

class StudyGoal(str, enum.Enum):
    EXAM_PREPARATION = "exam_preparation"
    SKILL_DEVELOPMENT = "skill_development"
    PERSONAL_ENRICHMENT = "personal_enrichment"
    LANGUAGE_LEARNING = "language_learning"

class UserProfile(Base):
    __tablename__ = "user_profiles"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), unique=True, index=True)
    name = Column(String, nullable=True)
    education_level = Column(String, nullable=True)
    user_type = Column(Enum(UserType), nullable=True)
    subjects = Column(ARRAY(String), nullable=True)
    study_goals = Column(ARRAY(Enum(StudyGoal)), nullable=True)
    profile_completion = Column(Integer, default=0)  # Percentage of completion
    
    # Relationship with User model
    user = relationship("User", back_populates="profile")