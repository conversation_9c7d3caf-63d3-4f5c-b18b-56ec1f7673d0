from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Any, Dict
from sqlalchemy import select

from app.core.config import settings
from app.db.session import get_db
from app.db.models.user import User
from app.firebase.client import firebase_client
from app.schemas.user import UserUpdate, User as UserSchema
from app.api.v1.endpoints.auth import oauth2_scheme

from app.core.security import verify_token

router = APIRouter()

async def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """Get current user based on JWT token"""
    try:
        # Verify token
        payload = verify_token(token)
        
        # Ensure it's an access token
        if payload.get("type") != "access":
            raise ValueError("Invalid token type")
            
        firebase_uid = payload.get("sub")
        if not firebase_uid:
            raise ValueError("Invalid token payload")
            
        # Get user from database
        query = select(User).where(User.firebase_uid == firebase_uid)
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
            
        return user
        
    except ValueError as ve:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(ve)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )

@router.get("/me", response_model=UserSchema)
async def read_user_me(
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get current user profile
    """
    user_data = {
        "id": current_user.id,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "phone_number": current_user.phone_number,
        "is_active": current_user.is_active,
        "is_verified": current_user.is_verified,
        "created_at": current_user.created_at,
        "updated_at": current_user.updated_at
    }
    
    return user_data

@router.put("/me", response_model=UserSchema)
async def update_user_me(
    user_in: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Update current user profile
    """
    # Update user data in database
    for field, value in user_in.dict(exclude_unset=True).items():
        setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    
    # Update user data in Firebase if needed
    update_data = {}
    if user_in.full_name:
        update_data["display_name"] = user_in.full_name
    if user_in.phone_number:
        update_data["phone_number"] = user_in.phone_number
        
    if update_data:
        try:
            await firebase_client.update_user(current_user.firebase_uid, **update_data)
        except Exception as e:
            # Log error but don't fail the request, as database is our source of truth
            print(f"Error updating Firebase user: {e}")
    
    return current_user

@router.delete("/me")
async def delete_user_me(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Delete current user account
    """
    try:
        # Delete user from Firebase
        await firebase_client.delete_user(current_user.firebase_uid)
        
        # Delete user from database
        db.delete(current_user)
        db.commit()
        
        return {"message": "User deleted successfully"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete user: {str(e)}"
        )
