import time
from typing import Callable, Optional
from fastapi import Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON><PERSON>App
from loguru import logger

from app.core.config import settings
from app.core.logging import get_request_id

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        request_id = get_request_id()
        start_time = time.time()
        
        # Bind request_id to context
        logger.bind(request_id=request_id)
        
        # Log request
        logger.info(f"Request: {request.method} {request.url.path}")
        
        try:
            response = await call_next(request)
            process_time = (time.time() - start_time) * 1000
            
            # Log response
            logger.info(
                f"Response: {request.method} {request.url.path}",
                duration=process_time,
                status_code=response.status_code
            )
            
            # Add custom headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.2f}ms"
            
            return response
        except Exception as e:
            process_time = (time.time() - start_time) * 1000
            logger.error(
                f"Error processing request: {str(e)}",
                duration=process_time,
                error=str(e)
            )
            raise

class RateLimitMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.rate_limit = settings.RATE_LIMIT_PER_MINUTE
        self._requests = {}

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if settings.ENABLE_RATE_LIMIT_MIDDLEWARE:
            client_ip = request.client.host
            current_time = time.time()
            
            # Clean old requests
            self._requests = {
                ip: times for ip, times in self._requests.items()
                if current_time - times[-1] < 60
            }
            
            # Check rate limit
            if client_ip in self._requests:
                if len(self._requests[client_ip]) >= self.rate_limit:
                    return Response(
                        content="Rate limit exceeded",
                        status_code=429
                    )
                self._requests[client_ip].append(current_time)
            else:
                self._requests[client_ip] = [current_time]
        
        return await call_next(request)

def setup_middleware(app):
    """Configure all middleware for the application"""
    
    # Add CORS middleware if enabled
    if settings.ENABLE_CORS_MIDDLEWARE and settings.BACKEND_CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    # Add GZip compression if enabled
    if settings.ENABLE_GZIP_MIDDLEWARE:
        app.add_middleware(GZipMiddleware, minimum_size=1000)

    # Add rate limiting if enabled
    if settings.ENABLE_RATE_LIMIT_MIDDLEWARE:
        app.add_middleware(RateLimitMiddleware)

    # Add request logging if enabled
    if settings.ENABLE_LOGGING_MIDDLEWARE:
        app.add_middleware(RequestLoggingMiddleware)