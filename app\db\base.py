# Import all the models, so that Base has them before being
# imported by Alembic
from app.db.base_class import Base  # noqa

# User related models
from app.db.models.user import User  # noqa
from app.db.models.user_preference import UserPreference  # noqa
from app.db.models.user_profile import UserProfile  # noqa

# Calendar related models
from app.db.models.event import Event  # noqa

# When adding new models, import them here
# Example:
# from app.db.models.new_model import NewModel  # noqa

